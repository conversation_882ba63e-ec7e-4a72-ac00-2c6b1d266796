import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import { Ground02Model } from '../entities/Ground02Model.js';
import { TreeModel } from '../entities/TreeModel.js';
import { Tree02Model } from '../entities/Tree02Model.js';
import { Tree03Model } from '../entities/Tree03Model.js';
import { Tree04Model } from '../entities/Tree04Model.js';
import { GrassModel } from '../entities/GrassModel.js';
import { RocksModel } from '../entities/RocksModel.js';

export interface ModelInstance {
  id: string;
  type: ModelType;
  name: string;
  model: TreeModel | Tree02Model | Tree03Model | Tree04Model | GrassModel | RocksModel;
  position: THREE.Vector3;
  rotation: THREE.Euler;
  scale: number;
  visible: boolean;
  createdAt: number;
}

export enum ModelType {
  TREE01 = 'tree01',
  TREE02 = 'tree02',
  TREE03 = 'tree03',
  TREE04 = 'tree04',
  GRASS = 'grass',
  ROCKS = 'rocks'
}

export interface SceneConfig {
  groundEnabled: boolean;
  maxInstances: number;
  defaultSpacing: number;
  autoArrange: boolean;
  performanceMode: boolean;
}

export interface SceneStats {
  totalInstances: number;
  visibleInstances: number;
  totalTriangles: number;
  instancesByType: Map<ModelType, number>;
  memoryUsage: number;
  renderTime: number;
  lastUpdate: number;
}

/**
 * 综合场景系统 - 地面、树木、草地、岩石组合管理
 * 
 * 核心功能：
 * - 基础地面展示（Ground_02模型）
 * - 多类型模型实例管理（树木、草地、岩石）
 * - 实时编辑和控制（位置、旋转、缩放）
 * - 性能监控和优化
 * - 直观的UI交互界面
 */
export class GroundTreeGrassRockCombat {
  private scene!: THREE.Scene;
  private camera!: THREE.PerspectiveCamera;
  public renderer!: THREE.WebGLRenderer; // 暴露给HTML使用
  private controls!: OrbitControls;
  private canvas: HTMLCanvasElement;
  private config: SceneConfig;

  // 基础地面
  private groundModel: Ground02Model | null = null;

  // 模型实例管理
  private instances: Map<string, ModelInstance> = new Map();
  private selectedInstanceId: string | null = null;

  // 场景统计
  private stats: SceneStats = {
    totalInstances: 0,
    visibleInstances: 0,
    totalTriangles: 0,
    instancesByType: new Map(),
    memoryUsage: 0,
    renderTime: 0,
    lastUpdate: Date.now()
  };

  // 性能监控
  private performanceClock = new THREE.Clock();
  private frameCount = 0;
  private animationId: number | null = null;

  // 事件回调
  private onInstanceAdded?: (instance: ModelInstance) => void;
  private onInstanceRemoved?: (instanceId: string) => void;
  private onInstanceSelected?: (instance: ModelInstance | null) => void;
  private onStatsUpdated?: (stats: SceneStats) => void;

  constructor(canvas: HTMLCanvasElement, config?: Partial<SceneConfig>) {
    this.canvas = canvas;

    // 默认配置
    this.config = {
      groundEnabled: true,
      maxInstances: 50,
      defaultSpacing: 2.0,
      autoArrange: false,
      performanceMode: false,
      ...config
    };

    // 初始化Three.js环境
    this.initializeThreeJS();

    // 初始化统计数据
    this.initializeStats();

    // 开始渲染循环
    this.startRenderLoop();
  }

  /**
   * 初始化Three.js环境
   */
  private initializeThreeJS(): void {
    // 创建场景
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(0x87CEEB); // 天蓝色背景

    // 创建相机
    this.camera = new THREE.PerspectiveCamera(
      75,
      this.canvas.clientWidth / this.canvas.clientHeight,
      0.1,
      1000
    );
    this.camera.position.set(15, 10, 15);
    this.camera.lookAt(0, 0, 0);

    // 创建渲染器
    this.renderer = new THREE.WebGLRenderer({
      canvas: this.canvas,
      antialias: true,
      alpha: true
    });
    this.renderer.setSize(this.canvas.clientWidth, this.canvas.clientHeight);
    this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    this.renderer.shadowMap.enabled = true;
    this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    this.renderer.outputColorSpace = THREE.SRGBColorSpace;

    // 添加光照
    this.setupLighting();

    // 设置控制器（如果需要的话）
    this.setupControls();
  }

  /**
   * 设置光照
   */
  private setupLighting(): void {
    // 环境光
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
    this.scene.add(ambientLight);

    // 主方向光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
    directionalLight.position.set(20, 20, 10);
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    directionalLight.shadow.camera.near = 0.1;
    directionalLight.shadow.camera.far = 100;
    directionalLight.shadow.camera.left = -20;
    directionalLight.shadow.camera.right = 20;
    directionalLight.shadow.camera.top = 20;
    directionalLight.shadow.camera.bottom = -20;
    this.scene.add(directionalLight);

    // 补充光源
    const fillLight = new THREE.DirectionalLight(0xffffff, 0.3);
    fillLight.position.set(-10, 10, -10);
    this.scene.add(fillLight);
  }

  /**
   * 设置控制器
   */
  private setupControls(): void {
    // 创建轨道控制器
    this.controls = new OrbitControls(this.camera, this.canvas);

    // 配置控制器参数
    this.controls.enableDamping = true; // 启用阻尼（惯性）
    this.controls.dampingFactor = 0.05; // 阻尼系数
    this.controls.screenSpacePanning = false; // 禁用屏幕空间平移

    // 设置距离限制
    this.controls.minDistance = 5;
    this.controls.maxDistance = 100;

    // 设置极角限制（垂直旋转）
    this.controls.maxPolarAngle = Math.PI / 2; // 限制不能看到地面下方

    // 设置目标点（相机看向的位置）
    this.controls.target.set(0, 0, 0);

    // 更新控制器
    this.controls.update();

    // 添加窗口大小调整监听
    window.addEventListener('resize', () => {
      this.handleResize();
    });
  }



  /**
   * 开始渲染循环
   */
  private startRenderLoop(): void {
    const animate = () => {
      this.animationId = requestAnimationFrame(animate);

      // 更新控制器（必须在渲染前调用）
      this.controls.update();

      // 更新场景
      this.update();

      // 渲染场景
      this.renderer.render(this.scene, this.camera);
    };

    animate();
  }

  /**
   * 清理资源
   */
  dispose(): void {
    // 停止渲染循环
    if (this.animationId !== null) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }

    // 清理所有实例
    this.instances.forEach(instance => {
      if (instance.model && typeof instance.model.dispose === 'function') {
        instance.model.dispose();
      }
    });
    this.instances.clear();

    // 清理地面模型
    if (this.groundModel && typeof this.groundModel.dispose === 'function') {
      this.groundModel.dispose();
    }

    // 清理控制器
    if (this.controls) {
      this.controls.dispose();
    }

    // 清理渲染器
    this.renderer.dispose();

    // 移除事件监听
    window.removeEventListener('resize', this.handleResize);
  }

  /**
   * 初始化统计数据
   */
  private initializeStats(): void {
    this.stats.instancesByType.clear();
    Object.values(ModelType).forEach(type => {
      this.stats.instancesByType.set(type, 0);
    });
  }



  /**
   * 初始化场景系统
   */
  async initializeScene(): Promise<void> {
    console.log('🌍 初始化综合场景系统...');
    
    try {
      // 加载基础地面
      if (this.config.groundEnabled) {
        await this.loadGroundModel();
      }
      
      console.log('✅ 综合场景系统初始化完成');
    } catch (error) {
      console.error('❌ 场景系统初始化失败:', error);
      throw error;
    }
  }

  /**
   * 加载基础地面模型
   */
  private async loadGroundModel(): Promise<void> {
    console.log('🏔️ 加载基础地面模型...');
    
    this.groundModel = new Ground02Model(this.scene, {
      name: 'SceneGround',
      position: new THREE.Vector3(20, 0, -20),
      scale: 1.0
    });
    
    this.groundModel.setEventCallbacks({
      onLoadComplete: () => {
        console.log('✅ 基础地面加载完成');
      }
    });
    
    await this.groundModel.initialize();
  }

  /**
   * 添加模型实例（支持多种参数格式）
   */
  async addModelInstance(
    type: ModelType | string,
    nameOrPosition?: string | THREE.Vector3 | {x: number, y: number, z: number},
    positionOrName?: THREE.Vector3 | {x: number, y: number, z: number} | string
  ): Promise<string> {
    // 检查实例数量限制
    if (this.instances.size >= this.config.maxInstances) {
      throw new Error(`已达到最大实例数量限制: ${this.config.maxInstances}`);
    }

    // 转换类型
    const modelType = this.parseModelType(type);
    const instanceId = this.generateInstanceId(modelType);

    // 解析参数
    let instanceName: string;
    let instancePosition: THREE.Vector3;

    // 判断第二个参数是名称还是位置
    if (typeof nameOrPosition === 'string') {
      // 格式: addModelInstance(type, name, position)
      instanceName = nameOrPosition;
      instancePosition = this.parsePosition(positionOrName) || this.getNextAvailablePosition();
    } else {
      // 格式: addModelInstance(type, position, name) 或 addModelInstance(type, position)
      instanceName = (typeof positionOrName === 'string') ? positionOrName : `${modelType}_${Date.now()}`;
      instancePosition = this.parsePosition(nameOrPosition) || this.getNextAvailablePosition();
    }

    console.log(`➕ 添加${modelType}实例: ${instanceName}`);

    try {
      // 创建模型实例
      const model = await this.createModelByType(modelType, instanceName, instancePosition);

      // 创建实例数据
      const instance: ModelInstance = {
        id: instanceId,
        type: modelType,
        name: instanceName,
        model,
        position: instancePosition.clone(),
        rotation: new THREE.Euler(0, 0, 0),
        scale: 1.0,
        visible: true,
        createdAt: Date.now()
      };
      
      // 添加到管理器
      this.instances.set(instanceId, instance);
      
      // 更新统计
      this.updateStats();
      
      // 触发事件
      if (this.onInstanceAdded) {
        this.onInstanceAdded(instance);
      }
      
      console.log(`✅ ${type}实例添加成功: ${instanceId}`);
      return instanceId;
      
    } catch (error) {
      console.error(`❌ 添加${type}实例失败:`, error);
      throw error;
    }
  }

  /**
   * 根据类型创建模型
   */
  private async createModelByType(
    type: ModelType, 
    name: string, 
    position: THREE.Vector3
  ): Promise<TreeModel | Tree02Model | Tree03Model | Tree04Model | GrassModel | RocksModel> {
    const config = {
      name,
      position: position.clone(),
      scale: this.getDefaultScale(type)
    };
    
    let model: TreeModel | Tree02Model | Tree03Model | Tree04Model | GrassModel | RocksModel;
    
    switch (type) {
      case ModelType.TREE01:
        model = new TreeModel(this.scene, config);
        break;
      case ModelType.TREE02:
        model = new Tree02Model(this.scene, config);
        break;
      case ModelType.TREE03:
        model = new Tree03Model(this.scene, config);
        break;
      case ModelType.TREE04:
        model = new Tree04Model(this.scene, config);
        break;
      case ModelType.GRASS:
        model = new GrassModel(this.scene, config);
        break;
      case ModelType.ROCKS:
        model = new RocksModel(this.scene, config);
        break;
      default:
        throw new Error(`不支持的模型类型: ${type}`);
    }
    
    // 设置加载完成回调
    if (type === ModelType.TREE04) {
      (model as Tree04Model).setCallbacks({
        onLoadComplete: () => {
          console.log(`📦 ${type}模型加载完成: ${name}`);
        }
      });
    } else {
      (model as TreeModel | Tree02Model | Tree03Model | GrassModel | RocksModel).setEventCallbacks({
        onLoadComplete: () => {
          console.log(`📦 ${type}模型加载完成: ${name}`);
        }
      });
    }
    
    await model.initialize();
    return model;
  }

  /**
   * 获取默认缩放值
   */
  private getDefaultScale(type: ModelType): number {
    switch (type) {
      case ModelType.TREE01:
      case ModelType.TREE02:
      case ModelType.TREE03:
      case ModelType.TREE04:
        return 0.02;
      case ModelType.GRASS:
        return 0.5;
      case ModelType.ROCKS:
        return 1.0;
      default:
        return 1.0;
    }
  }

  /**
   * 将字符串类型转换为ModelType枚举
   */
  private parseModelType(type: ModelType | string): ModelType {
    if (typeof type === 'string') {
      // 处理HTML传递的大写字符串
      const typeMap: {[key: string]: ModelType} = {
        'TREE01': ModelType.TREE01,
        'TREE02': ModelType.TREE02,
        'TREE03': ModelType.TREE03,
        'TREE04': ModelType.TREE04,
        'GRASS': ModelType.GRASS,
        'ROCKS': ModelType.ROCKS
      };

      const modelType = typeMap[type.toUpperCase()];
      if (!modelType) {
        throw new Error(`不支持的模型类型: ${type}`);
      }
      return modelType;
    }
    return type;
  }

  /**
   * 解析位置参数，支持THREE.Vector3和普通对象
   */
  private parsePosition(position?: THREE.Vector3 | {x: number, y: number, z: number} | string): THREE.Vector3 | null {
    if (!position || typeof position === 'string') {
      return null;
    }

    if (position instanceof THREE.Vector3) {
      return position;
    }

    // 处理普通对象 {x, y, z}
    if (typeof position === 'object' && 'x' in position && 'y' in position && 'z' in position) {
      return new THREE.Vector3(position.x, position.y, position.z);
    }

    return null;
  }

  /**
   * 获取下一个可用位置
   */
  private getNextAvailablePosition(): THREE.Vector3 {
    const spacing = this.config.defaultSpacing;
    const gridSize = Math.ceil(Math.sqrt(this.instances.size + 1));
    const row = Math.floor(this.instances.size / gridSize);
    const col = this.instances.size % gridSize;

    return new THREE.Vector3(
      (col - gridSize / 2) * spacing,
      0,
      (row - gridSize / 2) * spacing
    );
  }

  /**
   * 生成实例ID
   */
  private generateInstanceId(type: ModelType): string {
    return `${type}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * 更新统计数据
   */
  private updateStats(): void {
    this.stats.totalInstances = this.instances.size;
    this.stats.lastUpdate = Date.now();

    // 重置统计数据
    this.stats.visibleInstances = 0;
    this.stats.totalTriangles = 0;
    this.stats.instancesByType.clear();
    Object.values(ModelType).forEach(type => {
      this.stats.instancesByType.set(type, 0);
    });

    // 统计各类型实例数量和可见实例
    this.instances.forEach(instance => {
      const currentCount = this.stats.instancesByType.get(instance.type) || 0;
      this.stats.instancesByType.set(instance.type, currentCount + 1);

      if (instance.visible) {
        this.stats.visibleInstances++;

        // 估算三角面数（基于模型类型的大概值）
        const triangleEstimate = this.getTriangleEstimate(instance.type);
        this.stats.totalTriangles += triangleEstimate;
      }
    });

    // 触发统计更新事件
    if (this.onStatsUpdated) {
      this.onStatsUpdated(this.stats);
    }
  }

  /**
   * 根据模型类型估算三角面数
   */
  private getTriangleEstimate(type: ModelType): number {
    const estimates = {
      [ModelType.TREE01]: 2000,
      [ModelType.TREE02]: 2500,
      [ModelType.TREE03]: 2200,
      [ModelType.TREE04]: 2800,
      [ModelType.GRASS]: 500,
      [ModelType.ROCKS]: 1500
    };
    return estimates[type] || 1000;
  }

  /**
   * 删除模型实例
   */
  removeModelInstance(instanceId: string): boolean {
    const instance = this.instances.get(instanceId);
    if (!instance) {
      console.warn(`⚠️ 实例不存在: ${instanceId}`);
      return false;
    }

    console.log(`🗑️ 删除实例: ${instance.name}`);

    try {
      // 从场景中移除模型
      const model = this.getModelObject(instance.model);
      if (model) {
        this.scene.remove(model);
      }

      // 从管理器中移除
      this.instances.delete(instanceId);

      // 如果是当前选中的实例，清除选择
      if (this.selectedInstanceId === instanceId) {
        this.selectedInstanceId = null;
        if (this.onInstanceSelected) {
          this.onInstanceSelected(null);
        }
      }

      // 更新统计
      this.updateStats();

      // 触发事件
      if (this.onInstanceRemoved) {
        this.onInstanceRemoved(instanceId);
      }

      console.log(`✅ 实例删除成功: ${instanceId}`);
      return true;

    } catch (error) {
      console.error(`❌ 删除实例失败:`, error);
      return false;
    }
  }

  /**
   * 获取模型对象的统一方法
   */
  private getModelObject(model: TreeModel | Tree02Model | Tree03Model | Tree04Model | GrassModel | RocksModel): THREE.Group | null {
    if ('getModel' in model) {
      return model.getModel();
    }
    // Tree04Model没有getModel方法，需要直接访问内部模型
    return (model as any).model || null;
  }

  /**
   * 统一设置模型位置的方法
   */
  private setModelPosition(model: TreeModel | Tree02Model | Tree03Model | Tree04Model | GrassModel | RocksModel, position: THREE.Vector3): void {
    if (model instanceof Tree04Model) {
      // Tree04Model使用三个参数的setPosition方法
      model.setPosition(position.x, position.y, position.z);
    } else {
      // 其他模型使用Vector3参数的setPosition方法
      model.setPosition(position);
    }
  }

  /**
   * 统一设置模型旋转的方法
   */
  private setModelRotation(model: TreeModel | Tree02Model | Tree03Model | Tree04Model | GrassModel | RocksModel, rotation: THREE.Euler): void {
    if (model instanceof Tree04Model) {
      // Tree04Model使用三个参数的setRotation方法
      model.setRotation(rotation.x, rotation.y, rotation.z);
    } else {
      // 其他模型使用Euler参数的setRotation方法
      model.setRotation(rotation);
    }
  }

  /**
   * 统一设置模型可见性的方法
   */
  private setModelVisible(model: TreeModel | Tree02Model | Tree03Model | Tree04Model | GrassModel | RocksModel, visible: boolean): void {
    const modelObj = this.getModelObject(model);
    if (modelObj) {
      modelObj.visible = visible;
    }
  }

  /**
   * 统一更新模型的方法
   */
  private updateModel(model: TreeModel | Tree02Model | Tree03Model | Tree04Model | GrassModel | RocksModel): void {
    // 只有树模型有update方法
    if ('update' in model && typeof model.update === 'function') {
      model.update();
    }
  }

  /**
   * 选择实例
   */
  selectInstance(instanceId: string | null): void {
    if (instanceId && !this.instances.has(instanceId)) {
      console.warn(`⚠️ 实例不存在: ${instanceId}`);
      return;
    }

    this.selectedInstanceId = instanceId;
    const instance = instanceId ? this.instances.get(instanceId) || null : null;

    if (this.onInstanceSelected) {
      this.onInstanceSelected(instance);
    }

    console.log(`🎯 选择实例: ${instance ? instance.name : '无'}`);
  }

  /**
   * 更新实例位置
   */
  updateInstancePosition(instanceId: string, position: THREE.Vector3): boolean {
    const instance = this.instances.get(instanceId);
    if (!instance) {
      console.warn(`⚠️ 实例不存在: ${instanceId}`);
      return false;
    }

    try {
      instance.position.copy(position);
      this.setModelPosition(instance.model, position);
      console.log(`📍 更新实例位置: ${instance.name}`, position);
      return true;
    } catch (error) {
      console.error(`❌ 更新实例位置失败:`, error);
      return false;
    }
  }

  /**
   * 更新实例旋转
   */
  updateInstanceRotation(instanceId: string, rotation: THREE.Euler): boolean {
    const instance = this.instances.get(instanceId);
    if (!instance) {
      console.warn(`⚠️ 实例不存在: ${instanceId}`);
      return false;
    }

    try {
      instance.rotation.copy(rotation);
      this.setModelRotation(instance.model, rotation);
      console.log(`🔄 更新实例旋转: ${instance.name}`, rotation);
      return true;
    } catch (error) {
      console.error(`❌ 更新实例旋转失败:`, error);
      return false;
    }
  }

  /**
   * 更新实例缩放
   */
  updateInstanceScale(instanceId: string, scale: number): boolean {
    const instance = this.instances.get(instanceId);
    if (!instance) {
      console.warn(`⚠️ 实例不存在: ${instanceId}`);
      return false;
    }

    try {
      instance.scale = scale;
      instance.model.setScale(scale);
      console.log(`📏 更新实例缩放: ${instance.name}`, scale);
      return true;
    } catch (error) {
      console.error(`❌ 更新实例缩放失败:`, error);
      return false;
    }
  }

  /**
   * 复制实例
   */
  async duplicateInstance(instanceId: string): Promise<string | null> {
    const instance = this.instances.get(instanceId);
    if (!instance) {
      console.warn(`⚠️ 实例不存在: ${instanceId}`);
      return null;
    }

    try {
      // 计算新位置（稍微偏移）
      const newPosition = instance.position.clone();
      newPosition.x += this.config.defaultSpacing;

      // 创建新实例
      const newInstanceId = await this.addModelInstance(
        instance.type,
        newPosition,
        `${instance.name}_copy`
      );

      // 复制其他属性
      if (newInstanceId) {
        this.updateInstanceRotation(newInstanceId, instance.rotation.clone());
        this.updateInstanceScale(newInstanceId, instance.scale);
      }

      console.log(`📋 复制实例成功: ${instance.name} -> ${newInstanceId}`);
      return newInstanceId;

    } catch (error) {
      console.error(`❌ 复制实例失败:`, error);
      return null;
    }
  }

  /**
   * 清空所有实例
   */
  clearAllInstances(): void {
    console.log('🧹 清空所有实例...');

    const instanceIds = Array.from(this.instances.keys());
    instanceIds.forEach(id => {
      this.removeModelInstance(id);
    });

    console.log('✅ 所有实例已清空');
  }

  /**
   * 获取实例列表
   */
  getInstances(): ModelInstance[] {
    return Array.from(this.instances.values());
  }

  /**
   * 获取所有实例（别名方法，用于HTML兼容性）
   */
  getAllInstances(): ModelInstance[] {
    return this.getInstances();
  }

  /**
   * 根据ID获取实例
   */
  getInstance(instanceId: string): ModelInstance | null {
    return this.instances.get(instanceId) || null;
  }

  /**
   * 获取选中的实例
   */
  getSelectedInstance(): ModelInstance | null {
    return this.selectedInstanceId ? this.instances.get(this.selectedInstanceId) || null : null;
  }

  /**
   * 获取场景统计
   */
  getStats(): SceneStats {
    return { ...this.stats };
  }

  /**
   * 获取场景统计（别名方法，用于HTML兼容性）
   */
  getSceneStats(): SceneStats {
    return this.getStats();
  }

  /**
   * 切换地面可见性
   */
  toggleGroundVisibility(visible: boolean): void {
    if (this.groundModel) {
      this.groundModel.setVisibility(visible);
      console.log(`🌍 地面可见性: ${visible ? '显示' : '隐藏'}`);
    }
  }

  /**
   * 处理窗口大小调整（暴露给HTML使用）
   */
  handleResize(): void {
    const width = this.canvas.clientWidth;
    const height = this.canvas.clientHeight;

    this.camera.aspect = width / height;
    this.camera.updateProjectionMatrix();

    this.renderer.setSize(width, height);
  }

  /**
   * 更新实例位置（HTML友好的接口）
   */
  updateInstancePositionByNumbers(instanceId: string, x: number, y: number, z: number): boolean {
    return this.updateInstancePosition(instanceId, new THREE.Vector3(x, y, z));
  }

  /**
   * 更新实例旋转（HTML友好的接口）
   */
  updateInstanceRotationByNumbers(instanceId: string, x: number, y: number, z: number): boolean {
    return this.updateInstanceRotation(instanceId, new THREE.Euler(x, y, z));
  }



  /**
   * 更新场景（每帧调用）
   */
  update(): void {
    this.frameCount++;
    const deltaTime = this.performanceClock.getDelta();

    // 更新所有实例
    this.instances.forEach(instance => {
      if (instance.visible) {
        this.updateModel(instance.model);
      }
    });

    // 性能监控
    if (this.frameCount % 60 === 0) { // 每60帧更新一次性能统计
      this.stats.renderTime = deltaTime * 1000; // 转换为毫秒
      this.updateStats();
    }
  }

  /**
   * 设置事件回调
   */
  setEventCallbacks(callbacks: {
    onInstanceAdded?: (instance: ModelInstance) => void;
    onInstanceRemoved?: (instanceId: string) => void;
    onInstanceSelected?: (instance: ModelInstance | null) => void;
    onStatsUpdated?: (stats: SceneStats) => void;
  }): void {
    this.onInstanceAdded = callbacks.onInstanceAdded;
    this.onInstanceRemoved = callbacks.onInstanceRemoved;
    this.onInstanceSelected = callbacks.onInstanceSelected;
    this.onStatsUpdated = callbacks.onStatsUpdated;
  }

  /**
   * 切换实例可见性
   */
  toggleInstanceVisibility(instanceId: string, visible: boolean): boolean {
    const instance = this.instances.get(instanceId);
    if (!instance) {
      console.warn(`⚠️ 实例不存在: ${instanceId}`);
      return false;
    }

    try {
      instance.visible = visible;
      this.setModelVisible(instance.model, visible);
      console.log(`👁️ 实例可见性: ${instance.name} - ${visible ? '显示' : '隐藏'}`);
      return true;
    } catch (error) {
      console.error(`❌ 切换实例可见性失败:`, error);
      return false;
    }
  }

  /**
   * 自动排列实例
   */
  autoArrangeInstances(): void {
    console.log('🎯 自动排列实例...');

    const instances = Array.from(this.instances.values());
    const spacing = this.config.defaultSpacing;
    const gridSize = Math.ceil(Math.sqrt(instances.length));

    instances.forEach((instance, index) => {
      const row = Math.floor(index / gridSize);
      const col = index % gridSize;

      const newPosition = new THREE.Vector3(
        (col - gridSize / 2) * spacing,
        0,
        (row - gridSize / 2) * spacing
      );

      this.updateInstancePosition(instance.id, newPosition);
    });

    console.log('✅ 实例自动排列完成');
  }

  /**
   * 获取模型类型的显示名称
   */
  static getModelTypeDisplayName(type: ModelType): string {
    const names = {
      [ModelType.TREE01]: '树木01',
      [ModelType.TREE02]: '树木02',
      [ModelType.TREE03]: '树木03',
      [ModelType.TREE04]: '树木04',
      [ModelType.GRASS]: '草地',
      [ModelType.ROCKS]: '岩石'
    };
    return names[type] || type;
  }


}
