<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>地面-树木-草地-岩石综合场景系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            overflow: hidden;
        }

        .container {
            display: flex;
            height: 100vh;
            gap: 10px;
            padding: 10px;
        }

        /* 左侧控制面板 */
        .control-panel {
            width: 300px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            overflow-y: auto;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .control-panel h2 {
            color: #333;
            margin-bottom: 20px;
            text-align: center;
            font-size: 18px;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }

        .control-section {
            margin-bottom: 25px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .control-section h3 {
            color: #444;
            margin-bottom: 15px;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .model-selector {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }

        .model-btn {
            padding: 8px 12px;
            border: none;
            border-radius: 8px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .model-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .model-btn:active {
            transform: translateY(0);
        }

        .instance-list {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
        }

        .instance-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background-color 0.2s;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .instance-item:hover {
            background-color: #f5f5f5;
        }

        .instance-item.selected {
            background-color: #e3f2fd;
            border-left: 3px solid #667eea;
        }

        .instance-name {
            font-weight: 500;
            color: #333;
            font-size: 13px;
        }

        .instance-type {
            font-size: 11px;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .instance-actions {
            display: flex;
            gap: 5px;
        }

        .action-btn {
            padding: 4px 8px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            transition: all 0.2s;
        }

        .delete-btn {
            background: #ff4757;
            color: white;
        }

        .copy-btn {
            background: #2ed573;
            color: white;
        }

        .toggle-btn {
            background: #ffa502;
            color: white;
        }

        .action-btn:hover {
            transform: scale(1.1);
        }

        /* 中央3D视图 */
        .canvas-container {
            flex: 1;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 15px;
            overflow: hidden;
            position: relative;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        #canvas {
            width: 100%;
            height: 100%;
            display: block;
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            font-size: 18px;
            z-index: 1000;
        }

        .loading-spinner {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin-right: 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 右侧状态面板 */
        .status-panel {
            width: 280px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            overflow-y: auto;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .status-panel h2 {
            color: #333;
            margin-bottom: 20px;
            text-align: center;
            font-size: 18px;
            border-bottom: 2px solid #764ba2;
            padding-bottom: 10px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .stat-value {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 12px;
            opacity: 0.9;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .performance-info {
            background: rgba(255, 255, 255, 0.7);
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #764ba2;
        }

        .performance-info h3 {
            color: #444;
            margin-bottom: 10px;
            font-size: 14px;
        }

        .slider-control {
            margin-bottom: 12px;
        }

        .slider-control label {
            display: block;
            margin-bottom: 5px;
            color: #ccc;
            font-size: 12px;
        }

        .slider-control input[type="range"] {
            width: 100%;
            margin-bottom: 5px;
            background: #333;
            border-radius: 5px;
            outline: none;
            -webkit-appearance: none;
        }

        .slider-control input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: #4CAF50;
            cursor: pointer;
        }

        .slider-control input[type="range"]::-moz-range-thumb {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: #4CAF50;
            cursor: pointer;
            border: none;
        }

        .value-display {
            text-align: center;
            background: rgba(255,255,255,0.1);
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 11px;
            color: white;
        }

        .perf-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 13px;
        }

        .perf-label {
            color: #666;
        }

        .perf-value {
            color: #333;
            font-weight: 500;
        }

        /* 返回按钮 */
        .back-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 500;
            color: #333;
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            z-index: 1001;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(102, 126, 234, 0.6);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: rgba(102, 126, 234, 0.8);
        }
    </style>
</head>
<body>
    <a href="../index.html" class="back-btn">← 返回主页</a>
    
    <div class="container">
        <!-- 左侧控制面板 -->
        <div class="control-panel">
            <h2>🎮 场景控制</h2>
            
            <!-- 模型添加区域 -->
            <div class="control-section">
                <h3>🌲 添加模型</h3>
                <div class="model-selector">
                    <button class="model-btn" data-type="TREE01">Tree01</button>
                    <button class="model-btn" data-type="TREE02">Tree02</button>
                    <button class="model-btn" data-type="TREE03">Tree03</button>
                    <button class="model-btn" data-type="TREE04">Tree04</button>
                    <button class="model-btn" data-type="GRASS">Grass</button>
                    <button class="model-btn" data-type="ROCKS">Rocks</button>
                </div>
            </div>

            <!-- 实例管理区域 -->
            <div class="control-section">
                <h3>📦 实例管理</h3>
                <div class="instance-list" id="instanceList">
                    <div style="padding: 20px; text-align: center; color: #666; font-size: 13px;">
                        暂无实例，点击上方按钮添加模型
                    </div>
                </div>
            </div>

            <!-- 场景控制区域 -->
            <div class="control-section">
                <h3>🎛️ 场景控制</h3>
                <button class="model-btn" id="toggleGround" style="width: 100%; margin-bottom: 10px;">
                    🏔️ 切换地面显示
                </button>
                <button class="model-btn" id="clearAll" style="width: 100%; background: linear-gradient(45deg, #ff4757, #ff3742);">
                    🗑️ 清空所有实例
                </button>
            </div>
        </div>

        <!-- 中央3D视图 -->
        <div class="canvas-container">
            <canvas id="canvas"></canvas>
            <div class="loading-overlay" id="loadingOverlay">
                <div class="loading-spinner"></div>
                <span>正在初始化场景...</span>
            </div>
        </div>

        <!-- 右侧状态面板 -->
        <div class="status-panel">
            <h2>📊 场景状态</h2>
            
            <!-- 统计信息 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value" id="totalInstances">0</div>
                    <div class="stat-label">总实例数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="visibleInstances">0</div>
                    <div class="stat-label">可见实例</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="totalTriangles">0</div>
                    <div class="stat-label">三角面数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="memoryUsage">0MB</div>
                    <div class="stat-label">内存使用</div>
                </div>
            </div>

            <!-- 性能信息 -->
            <div class="performance-info">
                <h3>⚡ 性能监控</h3>
                <div class="perf-item">
                    <span class="perf-label">帧率:</span>
                    <span class="perf-value" id="fps">60 FPS</span>
                </div>
                <div class="perf-item">
                    <span class="perf-label">渲染时间:</span>
                    <span class="perf-value" id="renderTime">16ms</span>
                </div>
                <div class="perf-item">
                    <span class="perf-label">Draw Calls:</span>
                    <span class="perf-value" id="drawCalls">0</span>
                </div>
                <div class="perf-item">
                    <span class="perf-label">几何体数:</span>
                    <span class="perf-value" id="geometries">0</span>
                </div>
                <div class="perf-item">
                    <span class="perf-label">纹理数:</span>
                    <span class="perf-value" id="textures">0</span>
                </div>
            </div>

            <!-- 选中实例信息 -->
            <div class="performance-info" style="margin-top: 15px;">
                <h3>🎯 选中实例</h3>
                <div id="selectedInstanceInfo">
                    <div style="padding: 10px; text-align: center; color: #666; font-size: 13px;">
                        未选中任何实例
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        import { GroundTreeGrassRockCombat } from '../src/examples/ground-tree-grass-rock-combat.js';

        // 全局变量声明
        let sceneManager;

        class SceneManager {
            constructor() {
                this.canvas = document.getElementById('canvas');
                this.loadingOverlay = document.getElementById('loadingOverlay');
                this.sceneSystem = null;
                this.selectedInstanceId = null;
                this.groundVisible = true;
                
                this.initializeScene();
                this.setupEventListeners();
                this.startPerformanceMonitoring();
            }

            async initializeScene() {
                try {
                    console.log('🚀 初始化综合场景系统...');
                    
                    this.sceneSystem = new GroundTreeGrassRockCombat(this.canvas);
                    
                    // 设置事件回调
                    this.sceneSystem.setEventCallbacks({
                        onInstanceAdded: (instance) => {
                            console.log('✅ 实例添加成功:', instance.name);
                            this.updateInstanceList();
                            this.updateStats();
                        },
                        onInstanceRemoved: (instanceId) => {
                            console.log('🗑️ 实例删除成功:', instanceId);
                            this.updateInstanceList();
                            this.updateStats();
                            if (this.selectedInstanceId === instanceId) {
                                this.selectedInstanceId = null;
                                this.updateSelectedInstanceInfo();
                            }
                        },
                        onInstanceSelected: (instance) => {
                            this.selectedInstanceId = instance ? instance.id : null;
                            this.updateSelectedInstanceInfo();
                        },
                        onSceneUpdated: () => {
                            this.updateStats();
                        }
                    });
                    
                    // 初始化场景
                    await this.sceneSystem.initializeScene();
                    
                    this.hideLoading();
                    this.updateInstanceList();
                    this.updateStats();
                    
                    console.log('✅ 综合场景系统初始化完成');
                    
                } catch (error) {
                    console.error('❌ 场景初始化失败:', error);
                    this.hideLoading();
                }
            }

            setupEventListeners() {
                // 模型添加按钮
                document.querySelectorAll('.model-btn[data-type]').forEach(btn => {
                    btn.addEventListener('click', () => {
                        const modelType = btn.dataset.type;
                        this.addModelInstance(modelType);
                    });
                });

                // 地面显示切换
                document.getElementById('toggleGround').addEventListener('click', () => {
                    this.groundVisible = !this.groundVisible;
                    if (this.sceneSystem) {
                        this.sceneSystem.toggleGroundVisibility(this.groundVisible);
                    }
                });

                // 清空所有实例
                document.getElementById('clearAll').addEventListener('click', () => {
                    if (confirm('确定要清空所有实例吗？')) {
                        this.clearAllInstances();
                    }
                });

                // 窗口大小调整
                window.addEventListener('resize', () => {
                    if (this.sceneSystem) {
                        this.sceneSystem.handleResize();
                    }
                });
            }

            async addModelInstance(modelType) {
                if (!this.sceneSystem) return;

                try {
                    console.log(`🌲 添加${modelType}实例...`);

                    // 生成随机位置
                    const position = {
                        x: (Math.random() - 0.5) * 20,
                        y: 0,
                        z: (Math.random() - 0.5) * 20
                    };

                    const instanceId = await this.sceneSystem.addModelInstance(modelType, `${modelType}_${Date.now()}`, position);

                    if (instanceId) {
                        console.log(`✅ ${modelType}实例添加成功: ${instanceId}`);
                    }

                } catch (error) {
                    console.error(`❌ 添加${modelType}实例失败:`, error);
                }
            }

            clearAllInstances() {
                if (!this.sceneSystem) return;

                const instances = this.sceneSystem.getAllInstances();
                instances.forEach(instance => {
                    this.sceneSystem.removeModelInstance(instance.id);
                });

                this.selectedInstanceId = null;
                this.updateSelectedInstanceInfo();
            }

            updateInstanceList() {
                const instanceList = document.getElementById('instanceList');
                if (!this.sceneSystem) {
                    instanceList.innerHTML = '<div style="padding: 20px; text-align: center; color: #666; font-size: 13px;">场景未初始化</div>';
                    return;
                }

                const instances = this.sceneSystem.getAllInstances();

                if (instances.length === 0) {
                    instanceList.innerHTML = '<div style="padding: 20px; text-align: center; color: #666; font-size: 13px;">暂无实例，点击上方按钮添加模型</div>';
                    return;
                }

                instanceList.innerHTML = instances.map(instance => `
                    <div class="instance-item ${this.selectedInstanceId === instance.id ? 'selected' : ''}"
                         data-id="${instance.id}">
                        <div>
                            <div class="instance-name">${instance.name}</div>
                            <div class="instance-type">${instance.type}</div>
                        </div>
                        <div class="instance-actions">
                            <button class="action-btn toggle-btn" data-action="toggle" data-id="${instance.id}">
                                ${instance.visible ? '👁️' : '🙈'}
                            </button>
                            <button class="action-btn copy-btn" data-action="copy" data-id="${instance.id}">📋</button>
                            <button class="action-btn delete-btn" data-action="delete" data-id="${instance.id}">🗑️</button>
                        </div>
                    </div>
                `).join('');

                // 添加事件监听器
                instanceList.querySelectorAll('.instance-item').forEach(item => {
                    item.addEventListener('click', (e) => {
                        if (!e.target.classList.contains('action-btn')) {
                            const instanceId = item.dataset.id;
                            this.selectInstance(instanceId);
                        }
                    });
                });

                instanceList.querySelectorAll('.action-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        e.stopPropagation();
                        const action = btn.dataset.action;
                        const instanceId = btn.dataset.id;
                        this.handleInstanceAction(action, instanceId);
                    });
                });
            }

            selectInstance(instanceId) {
                this.selectedInstanceId = instanceId;
                this.updateInstanceList();
                this.updateSelectedInstanceInfo();

                if (this.sceneSystem) {
                    const instance = this.sceneSystem.getInstance(instanceId);
                    if (instance && this.sceneSystem.onInstanceSelected) {
                        this.sceneSystem.onInstanceSelected(instance);
                    }
                }
            }

            handleInstanceAction(action, instanceId) {
                if (!this.sceneSystem) return;

                switch (action) {
                    case 'delete':
                        if (confirm('确定要删除这个实例吗？')) {
                            this.sceneSystem.removeModelInstance(instanceId);
                        }
                        break;
                    case 'toggle':
                        const instance = this.sceneSystem.getInstance(instanceId);
                        if (instance) {
                            this.sceneSystem.toggleInstanceVisibility(instanceId, !instance.visible);
                            this.updateInstanceList();
                        }
                        break;
                    case 'copy':
                        this.copyInstance(instanceId);
                        break;
                }
            }

            async copyInstance(instanceId) {
                const instance = this.sceneSystem.getInstance(instanceId);
                if (!instance) return;

                try {
                    const newPosition = {
                        x: instance.position.x + (Math.random() - 0.5) * 4,
                        y: instance.position.y,
                        z: instance.position.z + (Math.random() - 0.5) * 4
                    };

                    const newInstanceId = await this.sceneSystem.addModelInstance(
                        instance.type,
                        `${instance.name}_copy_${Date.now()}`,
                        newPosition
                    );

                    if (newInstanceId) {
                        console.log(`📋 实例复制成功: ${newInstanceId}`);
                    }
                } catch (error) {
                    console.error('❌ 复制实例失败:', error);
                }
            }

            updateSelectedInstanceInfo() {
                const infoContainer = document.getElementById('selectedInstanceInfo');

                if (!this.selectedInstanceId || !this.sceneSystem) {
                    infoContainer.innerHTML = '<div style="padding: 10px; text-align: center; color: #666; font-size: 13px;">未选中任何实例</div>';
                    return;
                }

                const instance = this.sceneSystem.getInstance(this.selectedInstanceId);
                if (!instance) {
                    infoContainer.innerHTML = '<div style="padding: 10px; text-align: center; color: #666; font-size: 13px;">实例不存在</div>';
                    return;
                }

                infoContainer.innerHTML = `
                    <div class="perf-item">
                        <span class="perf-label">名称:</span>
                        <span class="perf-value">${instance.name}</span>
                    </div>
                    <div class="perf-item">
                        <span class="perf-label">类型:</span>
                        <span class="perf-value">${instance.type}</span>
                    </div>

                    <!-- 位置控制 -->
                    <div style="margin: 15px 0; padding: 10px; background: rgba(255,255,255,0.05); border-radius: 5px;">
                        <div style="font-weight: bold; margin-bottom: 8px; color: #4CAF50;">📍 位置控制</div>
                        <div class="slider-control">
                            <label for="posXSlider">位置 X：</label>
                            <input type="range" id="posXSlider" min="-20" max="20" step="0.1" value="${instance.position.x.toFixed(1)}">
                            <div class="value-display" id="posXValue">${instance.position.x.toFixed(1)}</div>
                        </div>
                        <div class="slider-control">
                            <label for="posYSlider">位置 Y：</label>
                            <input type="range" id="posYSlider" min="-10" max="10" step="0.1" value="${instance.position.y.toFixed(1)}">
                            <div class="value-display" id="posYValue">${instance.position.y.toFixed(1)}</div>
                        </div>
                        <div class="slider-control">
                            <label for="posZSlider">位置 Z：</label>
                            <input type="range" id="posZSlider" min="-20" max="20" step="0.1" value="${instance.position.z.toFixed(1)}">
                            <div class="value-display" id="posZValue">${instance.position.z.toFixed(1)}</div>
                        </div>
                    </div>

                    <!-- 旋转控制 -->
                    <div style="margin: 15px 0; padding: 10px; background: rgba(255,255,255,0.05); border-radius: 5px;">
                        <div style="font-weight: bold; margin-bottom: 8px; color: #2196F3;">🔄 旋转控制</div>
                        <div class="slider-control">
                            <label for="rotXSlider">旋转 X：</label>
                            <input type="range" id="rotXSlider" min="0" max="6.28" step="0.1" value="${instance.rotation.x.toFixed(2)}">
                            <div class="value-display" id="rotXValue">${(instance.rotation.x * 180 / Math.PI).toFixed(1)}°</div>
                        </div>
                        <div class="slider-control">
                            <label for="rotYSlider">旋转 Y：</label>
                            <input type="range" id="rotYSlider" min="0" max="6.28" step="0.1" value="${instance.rotation.y.toFixed(2)}">
                            <div class="value-display" id="rotYValue">${(instance.rotation.y * 180 / Math.PI).toFixed(1)}°</div>
                        </div>
                        <div class="slider-control">
                            <label for="rotZSlider">旋转 Z：</label>
                            <input type="range" id="rotZSlider" min="0" max="6.28" step="0.1" value="${instance.rotation.z.toFixed(2)}">
                            <div class="value-display" id="rotZValue">${(instance.rotation.z * 180 / Math.PI).toFixed(1)}°</div>
                        </div>
                    </div>

                    <!-- 缩放控制 -->
                    <div style="margin: 15px 0; padding: 10px; background: rgba(255,255,255,0.05); border-radius: 5px;">
                        <div style="font-weight: bold; margin-bottom: 8px; color: #FF9800;">📏 缩放控制</div>
                        <div class="slider-control">
                            <label for="scaleSlider">缩放：</label>
                            <input type="range" id="scaleSlider" min="0.1" max="5" step="0.1" value="${instance.scale.toFixed(2)}">
                            <div class="value-display" id="scaleValue">${instance.scale.toFixed(2)}</div>
                        </div>
                    </div>

                    <div class="perf-item">
                        <span class="perf-label">可见:</span>
                        <span class="perf-value">${instance.visible ? '是' : '否'}</span>
                    </div>
                `;

                // 添加滑块事件监听器
                setTimeout(() => {
                    this.setupSliderControls();
                }, 0);
            }

            setupSliderControls() {
                if (!this.selectedInstanceId || !this.sceneSystem) return;

                // 位置控制
                ['X', 'Y', 'Z'].forEach(axis => {
                    const slider = document.getElementById(`pos${axis}Slider`);
                    const value = document.getElementById(`pos${axis}Value`);
                    if (slider && value) {
                        slider.addEventListener('input', () => {
                            const newValue = parseFloat(slider.value);
                            const currentPos = this.sceneSystem.getInstance(this.selectedInstanceId).position;

                            if (axis === 'X') {
                                this.sceneSystem.updateInstancePositionByNumbers(this.selectedInstanceId, newValue, currentPos.y, currentPos.z);
                            } else if (axis === 'Y') {
                                this.sceneSystem.updateInstancePositionByNumbers(this.selectedInstanceId, currentPos.x, newValue, currentPos.z);
                            } else if (axis === 'Z') {
                                this.sceneSystem.updateInstancePositionByNumbers(this.selectedInstanceId, currentPos.x, currentPos.y, newValue);
                            }

                            value.textContent = newValue.toFixed(1);
                        });
                    }
                });

                // 旋转控制
                ['X', 'Y', 'Z'].forEach(axis => {
                    const slider = document.getElementById(`rot${axis}Slider`);
                    const value = document.getElementById(`rot${axis}Value`);
                    if (slider && value) {
                        slider.addEventListener('input', () => {
                            const newValue = parseFloat(slider.value);
                            const currentRot = this.sceneSystem.getInstance(this.selectedInstanceId).rotation;

                            if (axis === 'X') {
                                this.sceneSystem.updateInstanceRotationByNumbers(this.selectedInstanceId, newValue, currentRot.y, currentRot.z);
                            } else if (axis === 'Y') {
                                this.sceneSystem.updateInstanceRotationByNumbers(this.selectedInstanceId, currentRot.x, newValue, currentRot.z);
                            } else if (axis === 'Z') {
                                this.sceneSystem.updateInstanceRotationByNumbers(this.selectedInstanceId, currentRot.x, currentRot.y, newValue);
                            }

                            value.textContent = (newValue * 180 / Math.PI).toFixed(1) + '°';
                        });
                    }
                });

                // 缩放控制
                const scaleSlider = document.getElementById('scaleSlider');
                const scaleValue = document.getElementById('scaleValue');
                if (scaleSlider && scaleValue) {
                    scaleSlider.addEventListener('input', () => {
                        const scale = parseFloat(scaleSlider.value);
                        this.sceneSystem.updateInstanceScale(this.selectedInstanceId, scale);
                        scaleValue.textContent = scale.toFixed(2);
                    });
                }
            }

            updateStats() {
                if (!this.sceneSystem) return;

                const stats = this.sceneSystem.getSceneStats();

                document.getElementById('totalInstances').textContent = stats.totalInstances;
                document.getElementById('visibleInstances').textContent = stats.visibleInstances;
                document.getElementById('totalTriangles').textContent = stats.totalTriangles.toLocaleString();
                document.getElementById('memoryUsage').textContent = `${stats.memoryUsage.toFixed(1)}MB`;
            }

            startPerformanceMonitoring() {
                let frameCount = 0;
                let lastTime = performance.now();
                let fps = 60;

                const updatePerformance = () => {
                    frameCount++;
                    const currentTime = performance.now();
                    const deltaTime = currentTime - lastTime;

                    if (deltaTime >= 1000) {
                        fps = Math.round((frameCount * 1000) / deltaTime);
                        frameCount = 0;
                        lastTime = currentTime;

                        // 更新性能显示
                        document.getElementById('fps').textContent = `${fps} FPS`;
                        document.getElementById('renderTime').textContent = `${(1000 / fps).toFixed(1)}ms`;

                        if (this.sceneSystem && this.sceneSystem.renderer) {
                            const info = this.sceneSystem.renderer.info;
                            document.getElementById('drawCalls').textContent = info.render.calls;
                            document.getElementById('geometries').textContent = info.memory.geometries;
                            document.getElementById('textures').textContent = info.memory.textures;
                        }
                    }

                    requestAnimationFrame(updatePerformance);
                };

                updatePerformance();
            }

            hideLoading() {
                const overlay = document.getElementById('loadingOverlay');
                if (overlay) {
                    overlay.style.opacity = '0';
                    setTimeout(() => {
                        overlay.style.display = 'none';
                    }, 300);
                }
            }
        }

        // 启动场景管理器
        sceneManager = new SceneManager();
    </script>
</body>
</html>
